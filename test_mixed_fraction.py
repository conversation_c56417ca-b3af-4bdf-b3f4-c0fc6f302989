#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试带分数转换功能
"""

import sys
import os
import re

# 添加项目路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'quick_calc_rec_service', 'model'))

def convert_mixed_fraction_to_improper(expr):
    """
    将带分数表达式转换为假分数
    例如: (2+\\frac{2}{3}) -> \\frac{8}{3}

    Args:
        expr: 带分数表达式字符串

    Returns:
        假分数字符串，如果不是带分数格式则返回原字符串
    """
    # 匹配带分数格式: (整数+\frac{分子}{分母})
    pattern = r'\((\d+)\+\\frac\{(\d+)\}\{(\d+)\}\)'
    match = re.match(pattern, expr)

    if match:
        whole = int(match.group(1))
        numerator = int(match.group(2))
        denominator = int(match.group(3))

        # 转换为假分数: (整数 * 分母 + 分子) / 分母
        improper_numerator = whole * denominator + numerator

        return f"\\frac{{{improper_numerator}}}{{{denominator}}}"

    return expr

def test_convert_mixed_fraction_to_improper():
    """测试带分数转换为假分数的函数"""
    print("测试带分数转换为假分数功能...")

    # 测试用例
    test_cases = [
        ("(2+\\frac{2}{3})", "\\frac{8}{3}"),
        ("(1+\\frac{1}{2})", "\\frac{3}{2}"),
        ("(3+\\frac{1}{4})", "\\frac{13}{4}"),
        ("\\frac{5}{6}", "\\frac{5}{6}"),  # 不是带分数，应该保持不变
        ("123", "123"),  # 普通数字，应该保持不变
    ]

    for input_expr, expected in test_cases:
        result = convert_mixed_fraction_to_improper(input_expr)
        print(f"输入: {input_expr}")
        print(f"期望: {expected}")
        print(f"结果: {result}")
        print(f"测试{'通过' if result == expected else '失败'}")
        print("-" * 50)

def test_handle_answer_with_mixed_fraction():
    """测试handle_answer方法处理带分数的情况"""
    print("测试带分数转换逻辑...")

    # 模拟handle_answer中的带分数转换逻辑
    def simulate_mixed_fraction_logic(answer_list, answer):
        if answer_list:
            converted_answer_list = []
            for ans in answer_list:
                converted_ans = convert_mixed_fraction_to_improper(ans)
                converted_answer_list.append(converted_ans)

            # 如果转换后的答案与原答案不同，说明有带分数被转换
            if converted_answer_list != answer_list:
                # 检查转换后的答案是否与标准答案相等
                if isinstance(answer, str) and len(converted_answer_list) == 1:
                    if converted_answer_list[0] == answer:
                        return "0"  # 正确
                elif isinstance(answer, list) and len(converted_answer_list) == len(answer):
                    flag = True
                    for i in range(len(converted_answer_list)):
                        if converted_answer_list[i] != answer[i]:
                            flag = False
                            break
                    if flag:
                        return "0"  # 正确
        return "1"  # 错误或继续其他逻辑

    # 测试用例1: 带分数转换后与标准答案相等
    answer_list = ["(2+\\frac{2}{3})"]
    answer = "\\frac{8}{3}"

    result = simulate_mixed_fraction_logic(answer_list, answer)
    print(f"测试用例1:")
    print(f"answer_list: {answer_list}")
    print(f"answer: {answer}")
    print(f"转换后: {[convert_mixed_fraction_to_improper(ans) for ans in answer_list]}")
    print(f"期望flag为'0'，实际flag为'{result}'")
    print(f"测试{'通过' if result == '0' else '失败'}")
    print("-" * 50)

    # 测试用例2: 带分数转换后与标准答案不相等
    answer_list = ["(2+\\frac{2}{3})"]
    answer = "\\frac{7}{3}"

    result = simulate_mixed_fraction_logic(answer_list, answer)
    print(f"测试用例2:")
    print(f"answer_list: {answer_list}")
    print(f"answer: {answer}")
    print(f"转换后: {[convert_mixed_fraction_to_improper(ans) for ans in answer_list]}")
    print(f"期望flag不为'0'，实际flag为'{result}'")
    print(f"测试{'通过' if result != '0' else '失败'}")
    print("-" * 50)

if __name__ == "__main__":
    test_convert_mixed_fraction_to_improper()
    test_handle_answer_with_mixed_fraction()
